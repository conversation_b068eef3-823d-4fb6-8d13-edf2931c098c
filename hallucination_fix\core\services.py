"""
核心服务模块

幻觉修复工具的核心服务实现，通过导入方式复用主程序的服务组件。
"""

import os
import sys
import time
import logging
from typing import List, Optional, Dict, Any
from pathlib import Path

# 添加主程序路径到sys.path以便导入服务模块
current_dir = Path(__file__).parent.parent.parent
if str(current_dir) not in sys.path:
    sys.path.insert(0, str(current_dir))

from .models import (
    SubtitleEntry, LongEntryRefineRequest, LongEntryRefineResult,
    ProcessingStats, AudioSegment
)
from .config import RefineConfig
from .utils import (
    parse_srt_content, find_long_entries, calculate_entry_duration,
    create_temp_directory, cleanup_temp_files, detect_service_from_filename,
    generate_output_filename, parse_srt_time
)


class LongEntryRefinerService:
    """EvaTrans 幻觉修复服务

    核心服务类，负责协调整个幻觉修复流程：
    1. 检测长条目
    2. 切割音频片段
    3. 调用ASR服务重新转录
    4. 调用LLM服务分段文本
    5. 替换原始条目
    """
    
    def __init__(self, config: RefineConfig):
        """初始化服务
        
        Args:
            config: 配置对象
        """
        self.config = config
        self.logger = self._setup_logger()
        # 创建临时目录
        from .utils import create_temp_directory
        self.temp_dir = create_temp_directory()
        
        # 延迟导入服务模块（避免初始化时的导入错误）
        self._asr_services = {}
        self._llm_service = None
        self._audio_processor = None
        
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('hallucination_fixer')
        logger.setLevel(getattr(logging, self.config.log_level.upper(), logging.INFO))
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def _init_services(self):
        """初始化服务组件（延迟初始化）"""
        if self._asr_services or self._llm_service or self._audio_processor:
            return  # 已初始化
        
        try:
            # 导入并初始化ASR服务
            self._init_asr_services()
            
            # 导入并初始化LLM服务
            self._init_llm_service()
            
            # 导入并初始化音频处理器
            self._init_audio_processor()
            
        except Exception as e:
            self.logger.error(f"服务初始化失败: {e}")
            raise
    
    def _init_asr_services(self):
        """初始化ASR服务"""
        try:
            from services.asr import create_asr_service
            
            for asr_config in self.config.asr_services:
                if not asr_config.enabled:
                    continue
                
                service_config = {
                    'api_key': asr_config.api_key,
                    'max_retries': asr_config.max_retries,
                    'timeout': asr_config.timeout,
                    **asr_config.additional_params
                }
                
                try:
                    service = create_asr_service(asr_config.service_name, service_config)
                    self._asr_services[asr_config.service_name] = service
                    self.logger.info(f"ASR服务初始化成功: {asr_config.service_name}")
                except Exception as e:
                    self.logger.warning(f"ASR服务初始化失败 {asr_config.service_name}: {e}")
            
        except ImportError as e:
            self.logger.error(f"无法导入ASR服务模块: {e}")
            raise
    
    def _init_llm_service(self):
        """初始化LLM服务"""
        try:
            from services.llm import MultiLLMService
            
            # 使用主程序的配置文件路径
            config_path = os.path.join(os.path.expanduser("~"), ".evatrans_gui", "config.json")
            self._llm_service = MultiLLMService(config_path)
            self.logger.info("LLM服务初始化成功")
            
        except ImportError as e:
            self.logger.error(f"无法导入LLM服务模块: {e}")
            raise
    
    def _init_audio_processor(self):
        """初始化音频处理器"""
        try:
            from services.audio import AudioProcessor
            
            self._audio_processor = AudioProcessor()
            self.logger.info("音频处理器初始化成功")
            
        except ImportError as e:
            self.logger.error(f"无法导入音频处理模块: {e}")
            raise
    
    def process_request(self, request: LongEntryRefineRequest) -> LongEntryRefineResult:
        """处理长条目精炼请求
        
        Args:
            request: 精炼请求
            
        Returns:
            LongEntryRefineResult: 处理结果
        """
        start_time = time.time()
        
        try:
            # 初始化服务
            self._init_services()
            
            # 创建临时目录
            self.temp_dir = create_temp_directory()
            
            # 测试模式：只检测长条目
            if request.test_mode:
                return self._test_mode_process(request)
            
            # 完整处理流程
            return self._full_process(request)
            
        except Exception as e:
            self.logger.error(f"处理请求失败: {e}")
            return LongEntryRefineResult(
                success=False,
                processed_entries=0,
                refined_entries=0,
                error_message=str(e),
                processing_time=time.time() - start_time
            )
        finally:
            # 清理临时文件
            if self.temp_dir:
                cleanup_temp_files(self.temp_dir)
    
    def _test_mode_process(self, request: LongEntryRefineRequest) -> LongEntryRefineResult:
        """测试模式处理：只检测长条目"""
        subtitle_path = os.path.join(request.task_folder, request.subtitle_file)
        
        if not os.path.exists(subtitle_path):
            raise FileNotFoundError(f"字幕文件不存在: {subtitle_path}")
        
        # 读取并解析字幕文件
        with open(subtitle_path, 'r', encoding='utf-8') as f:
            srt_content = f.read()
        
        entries = parse_srt_content(srt_content)
        long_entries = find_long_entries(entries, request.threshold_seconds)
        
        self.logger.info(f"检测到 {len(long_entries)} 个长条目（阈值: {request.threshold_seconds}秒）")
        
        for entry in long_entries:
            duration = calculate_entry_duration(entry)
            self.logger.info(f"条目#{entry.index}: {duration:.1f}秒 - {entry.text[:50]}...")
        
        return LongEntryRefineResult(
            success=True,
            processed_entries=len(entries),
            refined_entries=len(long_entries),
            processing_time=0.0
        )
    
    def _full_process(self, request: LongEntryRefineRequest) -> LongEntryRefineResult:
        """完整处理流程"""
        start_time = time.time()

        # 1. 检测长条目
        subtitle_path = os.path.join(request.task_folder, request.subtitle_file)
        audio_path = os.path.join(request.task_folder, request.audio_file)

        if not os.path.exists(subtitle_path):
            raise FileNotFoundError(f"字幕文件不存在: {subtitle_path}")

        if not os.path.exists(audio_path):
            raise FileNotFoundError(f"音频文件不存在: {audio_path}")

        # 读取并解析字幕文件
        with open(subtitle_path, 'r', encoding='utf-8') as f:
            srt_content = f.read()

        entries = parse_srt_content(srt_content)
        long_entries = find_long_entries(entries, request.threshold_seconds)

        if not long_entries:
            self.logger.info("未发现长条目，无需处理")
            return LongEntryRefineResult(
                success=True,
                processed_entries=len(entries),
                refined_entries=0,
                processing_time=time.time() - start_time
            )

        self.logger.info(f"发现 {len(long_entries)} 个长条目，开始处理")

        # 2. 切割音频片段
        audio_segments = self._extract_audio_segments(audio_path, long_entries)

        # 3. 处理每个音频片段
        refined_entries = []
        deleted_entries = []

        for i, (entry, segment) in enumerate(zip(long_entries, audio_segments)):
            try:
                self.logger.info(f"处理条目 #{entry.index} ({i+1}/{len(long_entries)})")

                # ASR转录并生成parsed.json
                parsed_json_path = self._transcribe_audio_segment(segment)

                if parsed_json_path == "DELETE":
                    # 标记为删除
                    deleted_entries.append(entry)
                    self.logger.info(f"条目 #{entry.index} 标记为删除")
                    continue
                elif not parsed_json_path:
                    # 转录失败，也标记为删除
                    deleted_entries.append(entry)
                    self.logger.warning(f"条目 #{entry.index} 转录失败，标记为删除")
                    continue

                # LLM分段和字幕生成
                srt_file_path = self._segment_text_with_llm(parsed_json_path)
                if not srt_file_path:
                    deleted_entries.append(entry)
                    self.logger.warning(f"条目 #{entry.index} 字幕生成失败，标记为删除")
                    continue

                # 创建新的字幕条目
                new_entries = self._create_new_subtitle_entries(entry, srt_file_path)
                if new_entries:
                    refined_entries.extend(new_entries)
                    self.logger.debug(f"条目 #{entry.index} 成功生成 {len(new_entries)} 个新条目，索引范围: {[e.index for e in new_entries]}")
                else:
                    deleted_entries.append(entry)
                    self.logger.warning(f"条目 #{entry.index} 未生成有效的新条目，标记为删除")

            except Exception as e:
                deleted_entries.append(entry)
                self.logger.error(f"处理条目 #{entry.index} 失败: {e}，标记为删除")
                continue

        # 4. 替换原始条目并生成新文件
        if refined_entries or deleted_entries:
            output_file, comparison_report = self._replace_entries_and_save(
                entries, long_entries, refined_entries, deleted_entries,
                request.task_folder, request.subtitle_file
            )

            # 5. 生成对比报告
            report_file = self._generate_comparison_report(
                comparison_report, request.task_folder, request.subtitle_file
            )
        else:
            output_file = None
            report_file = None

        return LongEntryRefineResult(
            success=True,
            processed_entries=len(entries),
            refined_entries=len(refined_entries),
            output_file=output_file,
            processing_time=time.time() - start_time
        )
    
    def get_available_asr_services(self) -> List[str]:
        """获取可用的ASR服务列表"""
        self._init_services()
        return list(self._asr_services.keys())
    
    def get_available_llm_services(self) -> bool:
        """检查LLM服务是否可用"""
        self._init_services()
        return self._llm_service is not None

    def _extract_audio_segments(self, audio_path: str, long_entries: List[SubtitleEntry]) -> List[AudioSegment]:
        """提取音频片段"""
        self.logger.info(f"开始提取 {len(long_entries)} 个音频片段")
        segments = []

        for entry in long_entries:
            try:
                # 解析时间戳
                start_seconds = self._parse_timestamp_to_seconds(entry.start_time)
                end_seconds = self._parse_timestamp_to_seconds(entry.end_time)

                # 创建临时文件路径
                segment_filename = f"segment_{entry.index}_{int(start_seconds)}_{int(end_seconds)}.wav"
                segment_path = os.path.join(self.temp_dir, segment_filename)

                # 使用FFmpeg切割音频片段
                success = self._extract_audio_segment_with_ffmpeg(
                    audio_path, segment_path, start_seconds, end_seconds
                )

                if success and os.path.exists(segment_path):
                    segment = AudioSegment(
                        file_path=segment_path,
                        start_time=start_seconds,
                        end_time=end_seconds,
                        duration=end_seconds - start_seconds,
                        original_entry_index=entry.index
                    )
                    segments.append(segment)
                    self.logger.debug(f"成功提取音频片段: {segment_filename}")
                else:
                    self.logger.warning(f"音频片段提取失败: 条目#{entry.index}")

            except Exception as e:
                self.logger.error(f"提取音频片段失败 (条目#{entry.index}): {e}")
                continue

        self.logger.info(f"成功提取 {len(segments)} 个音频片段")
        return segments

    def _parse_timestamp_to_seconds(self, timestamp: str) -> float:
        """将SRT时间戳转换为秒数"""
        # 格式: HH:MM:SS,mmm
        time_part, ms_part = timestamp.split(',')
        hours, minutes, seconds = map(int, time_part.split(':'))
        milliseconds = int(ms_part)

        total_seconds = hours * 3600 + minutes * 60 + seconds + milliseconds / 1000.0
        return total_seconds

    def _transcribe_audio_segment(self, segment: AudioSegment) -> Optional[str]:
        """转录音频片段并生成parsed.json文件

        Args:
            segment: 音频片段信息

        Returns:
            str: parsed.json文件路径，失败返回None
        """
        try:
            # 使用第一个可用的ASR服务
            asr_service_name = list(self._asr_services.keys())[0]
            asr_service = self._asr_services[asr_service_name]

            self.logger.debug(f"使用 {asr_service_name} 转录音频片段: {segment.file_path}")

            # 创建ASR请求
            from services.asr import ASRRequest
            asr_request = ASRRequest(
                audio_file_path=segment.file_path,
                language='auto',  # 使用自动语言检测，避免添加language_code
                enable_diarization=True  # ElevenLabs免费模式要求必须为True
            )

            # 执行转录
            response = asr_service.transcribe(asr_request)

            # 检查转录状态
            from services.asr import ASRStatus



            if response.status == ASRStatus.COMPLETED:
                if hasattr(response, 'full_text') and response.full_text:
                    # 转录成功且有内容
                    self.logger.debug(f"转录成功: {response.full_text[:50]}...")

                    # 保存为主程序标准JSON格式
                    json_path = self._save_asr_result_as_json(response, asr_service_name, segment)
                    if not json_path:
                        self.logger.error("保存ASR结果为JSON失败")
                        return None

                    # 使用TranscriptionParser生成parsed.json
                    parsed_path = self._generate_parsed_json(json_path, asr_service_name)
                    if not parsed_path:
                        self.logger.error("生成parsed.json失败")
                        return None

                    return parsed_path
                else:
                    # 转录成功但内容为空（静音片段），标记为删除
                    self.logger.info(f"音频片段无语音内容（静音），标记为删除")
                    return "DELETE"  # 特殊标记表示删除
            else:
                # 转录失败，标记为删除
                error_msg = getattr(response, 'error_message', '未知错误')
                self.logger.warning(f"转录失败: 状态={response.status}, 错误={error_msg}，标记为删除")
                return "DELETE"  # 特殊标记表示删除

        except Exception as e:
            self.logger.error(f"转录音频片段失败: {e}")
            import traceback
            self.logger.error(f"详细错误信息: {traceback.format_exc()}")
            return None

    def _save_asr_result_as_json(self, response, service_name: str, segment: AudioSegment) -> Optional[str]:
        """将ASR响应保存为主程序标准JSON格式"""
        try:
            import json
            import time

            # 构建标准JSON数据结构（与主程序一致）
            result_data = {
                'success': True,
                'service': service_name.lower(),
                'transcription_id': response.transcription_id,
                'text': response.full_text,
                'words': response.words,
                'language_detected': response.language_detected,
                'confidence': response.confidence,
                'processing_time': response.processing_time,
                'metadata': response.metadata,
                'created_at': response.created_at or time.time()
            }

            # 生成临时JSON文件路径
            json_filename = f"segment_{segment.original_entry_index}_{service_name}.json"
            json_path = os.path.join(self.temp_dir, json_filename)

            # 保存JSON文件
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(result_data, f, ensure_ascii=False, indent=2)

            self.logger.debug(f"ASR结果已保存: {json_path}")
            return json_path

        except Exception as e:
            self.logger.error(f"保存ASR结果失败: {e}")
            return None

    def _generate_parsed_json(self, json_path: str, service_name: str) -> Optional[str]:
        """使用TranscriptionParser生成parsed.json文件"""
        try:
            from services.asr import TranscriptionParser

            # 创建解析器
            parser = TranscriptionParser()

            # 生成parsed.json文件路径
            base_name = os.path.splitext(os.path.basename(json_path))[0]
            parsed_filename = f"{base_name}-parsed.json"
            parsed_path = os.path.join(self.temp_dir, parsed_filename)

            # 解析并转换为模板格式
            result = parser.parse_raw_json_to_template(json_path, parsed_path)

            if result:
                self.logger.debug(f"parsed.json已生成: {parsed_path}")
                return parsed_path
            else:
                self.logger.error(f"生成parsed.json失败")
                return None

        except Exception as e:
            self.logger.error(f"生成parsed.json失败: {e}")
            return None

    def _save_empty_asr_result_as_json(self, segment: AudioSegment) -> str:
        """为静音片段创建空内容的parsed.json文件"""
        try:
            # 创建空的parsed.json内容
            parsed_data = {
                "full_text": "",
                "words": [],
                "service": "elevenlabs",
                "confidence": 0.0,
                "language_detected": None,
                "processing_time": 0.0,
                "metadata": {
                    "note": "静音片段，无语音内容"
                }
            }

            # 保存parsed.json文件
            parsed_filename = f"parsed_{segment.original_entry_index}_{int(segment.start_time)}_{int(segment.end_time)}.json"
            parsed_path = os.path.join(self.temp_dir, parsed_filename)

            with open(parsed_path, 'w', encoding='utf-8') as f:
                json.dump(parsed_data, f, ensure_ascii=False, indent=2)

            self.logger.debug(f"创建空内容parsed.json: {parsed_path}")
            return parsed_path

        except Exception as e:
            self.logger.error(f"创建空内容parsed.json失败: {e}")
            return None

    def _segment_text_with_llm(self, parsed_json_path: str) -> Optional[str]:
        """使用完整的字幕生成服务进行LLM分段和时间戳对齐

        Args:
            parsed_json_path: parsed.json文件路径

        Returns:
            str: 生成的SRT文件路径，失败返回None
        """
        try:
            from services.subtitle.llm_service import SubtitleLLMService

            # 创建字幕生成服务
            subtitle_service = SubtitleLLMService()

            self.logger.debug(f"使用SubtitleLLMService处理: {parsed_json_path}")

            # 调用完整的字幕生成流程
            result = subtitle_service.generate_subtitle(parsed_json_path)

            if result.get('success', False):
                # 获取生成的SRT文件路径
                results = result.get('results', [])
                if results:
                    # 使用第一个生成的SRT文件
                    first_result = results[0]
                    srt_path = first_result.get('srt_path')

                    if srt_path and os.path.exists(srt_path):
                        self.logger.debug(f"字幕生成成功: {srt_path}")
                        return srt_path
                    else:
                        self.logger.warning(f"SRT文件路径无效或文件不存在: {srt_path}")
                        return None
                else:
                    self.logger.warning("字幕生成成功但未返回结果列表")
                    return None
            else:
                error_msg = result.get('error', '未知错误')
                self.logger.warning(f"字幕生成失败: {error_msg}")
                return None

        except Exception as e:
            self.logger.error(f"LLM分段和字幕生成失败: {e}")
            return None

    def _create_new_subtitle_entries(self, original_entry: SubtitleEntry, srt_file_path: str) -> List[SubtitleEntry]:
        """从生成的SRT文件创建新的字幕条目，并调整到原始条目的时间范围

        Args:
            original_entry: 原始长条目
            srt_file_path: 生成的SRT文件路径

        Returns:
            List[SubtitleEntry]: 调整后的新字幕条目列表
        """
        if not srt_file_path or not os.path.exists(srt_file_path):
            self.logger.error(f"SRT文件不存在: {srt_file_path}")
            return []

        try:
            # 解析生成的SRT文件
            from .utils import parse_srt_content

            with open(srt_file_path, 'r', encoding='utf-8') as f:
                srt_content = f.read()

            generated_entries = parse_srt_content(srt_content)
            if not generated_entries:
                self.logger.warning("生成的SRT文件为空")
                return []

            # 获取原始条目的时间范围
            original_start = self._parse_timestamp_to_seconds(original_entry.start_time)
            original_end = self._parse_timestamp_to_seconds(original_entry.end_time)
            original_duration = original_end - original_start

            # 获取生成条目的时间范围
            generated_start = self._parse_timestamp_to_seconds(generated_entries[0].start_time)
            generated_end = self._parse_timestamp_to_seconds(generated_entries[-1].end_time)
            generated_duration = generated_end - generated_start

            if generated_duration <= 0:
                self.logger.error("生成的字幕条目时间范围无效")
                return []

            # 计算时间缩放比例
            time_scale = original_duration / generated_duration

            # 调整每个条目的时间戳到原始范围
            adjusted_entries = []
            for i, entry in enumerate(generated_entries):
                # 计算相对于生成起始时间的偏移
                entry_start = self._parse_timestamp_to_seconds(entry.start_time)
                entry_end = self._parse_timestamp_to_seconds(entry.end_time)

                start_offset = (entry_start - generated_start) * time_scale
                end_offset = (entry_end - generated_start) * time_scale

                # 映射到原始时间范围
                new_start = original_start + start_offset
                new_end = original_start + end_offset

                # 创建调整后的条目
                adjusted_entry = SubtitleEntry(
                    index=original_entry.index + i * 0.001,  # 使用小数索引避免冲突
                    start_time=self._seconds_to_timestamp(new_start),
                    end_time=self._seconds_to_timestamp(new_end),
                    text=entry.text
                )
                adjusted_entries.append(adjusted_entry)

            self.logger.debug(f"成功调整 {len(adjusted_entries)} 个字幕条目到原始时间范围")
            return adjusted_entries

        except Exception as e:
            self.logger.error(f"创建新字幕条目失败: {e}")
            return []

    def _seconds_to_timestamp(self, seconds: float) -> str:
        """将秒数转换为SRT时间戳格式"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        milliseconds = int((seconds % 1) * 1000)

        return f"{hours:02d}:{minutes:02d}:{secs:02d},{milliseconds:03d}"

    def _replace_entries_and_save(self, all_entries: List[SubtitleEntry],
                                 long_entries: List[SubtitleEntry],
                                 refined_entries: List[SubtitleEntry],
                                 deleted_entries: List[SubtitleEntry],
                                 task_folder: str, subtitle_file: str) -> tuple[str, dict]:
        """替换长条目并保存新文件，返回输出文件名和对比报告"""
        # 创建长条目索引集合
        long_entry_indices = {entry.index for entry in long_entries}
        deleted_entry_indices = {entry.index for entry in deleted_entries}

        # 按原始条目索引分组精炼后的条目
        refined_by_original = {}
        for refined_entry in refined_entries:
            original_index = int(refined_entry.index)  # 取整数部分作为原始索引
            if original_index not in refined_by_original:
                refined_by_original[original_index] = []
            refined_by_original[original_index].append(refined_entry)

        # 构建新的条目列表和对比报告
        new_entries = []
        comparison_report = {
            'replaced': [],  # 替换的条目
            'deleted': [],   # 删除的条目
            'total_original': len(all_entries),
            'total_new': 0   # 稍后计算
        }

        for entry in all_entries:
            if entry.index in long_entry_indices:
                if entry.index in deleted_entry_indices:
                    # 删除条目
                    comparison_report['deleted'].append({
                        'original_index': entry.index,
                        'original_text': entry.text,
                        'original_duration': f"{entry.start_time} -> {entry.end_time}",
                        'reason': '静音或转录失败'
                    })
                    self.logger.info(f"删除条目 #{entry.index}")
                elif entry.index in refined_by_original:
                    # 替换为精炼后的条目
                    refined_list = refined_by_original[entry.index]

                    # 记录新条目在列表中的起始位置（用于后续添加位置信息）
                    start_position = len(new_entries)
                    new_entries.extend(refined_list)

                    comparison_report['replaced'].append({
                        'original_index': entry.index,
                        'original_text': entry.text,
                        'original_duration': f"{entry.start_time} -> {entry.end_time}",
                        'new_count': len(refined_list),
                        'new_entries': [
                            {
                                'text': new_entry.text,
                                'duration': f"{new_entry.start_time} -> {new_entry.end_time}",
                                'list_position': start_position + i  # 在列表中的位置
                            }
                            for i, new_entry in enumerate(refined_list)
                        ]
                    })
                    self.logger.info(f"替换条目 #{entry.index} 为 {len(refined_list)} 个新条目")
                else:
                    # 未处理的长条目，保留原条目
                    new_entries.append(entry)
                    self.logger.warning(f"长条目 #{entry.index} 未处理，保留原条目")
            else:
                # 保留原条目
                new_entries.append(entry)

        # 重新编号
        for i, entry in enumerate(new_entries, 1):
            entry.index = i

        # 更新对比报告中新条目的实际位置信息
        for replaced_item in comparison_report['replaced']:
            for new_entry_info in replaced_item['new_entries']:
                list_pos = new_entry_info['list_position']
                new_entry_info['new_position'] = list_pos + 1  # 转换为1基索引

        # 更新对比报告中的新条目总数
        comparison_report['total_new'] = len(new_entries)

        # 生成输出文件名
        base_name = os.path.splitext(subtitle_file)[0]
        output_file = f"{base_name}_fixed.srt"
        output_path = os.path.join(task_folder, output_file)

        # 保存文件
        self._save_srt_file(new_entries, output_path)

        self.logger.info(f"修复后的字幕已保存: {output_file}")
        self.logger.info(f"条目统计: 原始{comparison_report['total_original']} -> 新{comparison_report['total_new']}")
        self.logger.info(f"替换{len(comparison_report['replaced'])}个，删除{len(comparison_report['deleted'])}个")

        return output_file, comparison_report

    def _generate_comparison_report(self, comparison_report: dict, task_folder: str, subtitle_file: str) -> str:
        """生成现代化卡片式对比报告"""
        base_name = os.path.splitext(subtitle_file)[0]
        report_file = f"{base_name}_comparison_report.txt"
        report_path = os.path.join(task_folder, report_file)

        with open(report_path, 'w', encoding='utf-8') as f:
            # 报告头部
            f.write("╔══════════════════════════════════════════════════════════════════════════════╗\n")
            f.write("║                    📊 EvaTrans 幻觉修复对比报告                             ║\n")
            f.write("║                                                                              ║\n")
            f.write(f"║  📂 {subtitle_file:<66} ║\n")
            f.write(f"║  🕐 {time.strftime('%Y-%m-%d %H:%M:%S'):<66} ║\n")
            f.write("╚══════════════════════════════════════════════════════════════════════════════╝\n\n")

            # 处理概览
            total_long_entries = len(comparison_report['replaced']) + len(comparison_report['deleted'])
            total_new_entries = sum(item['new_count'] for item in comparison_report['replaced'])
            net_change = comparison_report['total_new'] - comparison_report['total_original']
            net_change_str = f"(+{net_change})" if net_change > 0 else f"({net_change})" if net_change < 0 else "(无变化)"

            f.write("╭─ 📊 处理概览 ────────────────────────────────────────────────────────────────╮\n")
            f.write("│                                                                              │\n")
            f.write(f"│  📄 条目统计:  {comparison_report['total_original']} → {comparison_report['total_new']} {net_change_str:<10} 🎯 长条目: {total_long_entries}个{' ' * (25 - len(str(total_long_entries)))}│\n")
            f.write(f"│  🔄 成功替换:  {len(comparison_report['replaced'])}个 → {total_new_entries}个新条目{' ' * (6 - len(str(total_new_entries)))} 🗑️  删除失败: {len(comparison_report['deleted'])}个{' ' * (22 - len(str(len(comparison_report['deleted']))))}│\n")
            f.write("│                                                                              │\n")
            f.write("╰──────────────────────────────────────────────────────────────────────────────╯\n\n")

            # 替换详情
            if comparison_report['replaced']:
                f.write("╭─ 🔄 替换详情 ────────────────────────────────────────────────────────────────╮\n")
                f.write("│                                                                              │\n")

                for item in comparison_report['replaced']:
                    # 计算新条目的位置范围
                    new_positions = [entry['new_position'] for entry in item['new_entries']]
                    if len(new_positions) == 1:
                        position_range = f"#{new_positions[0]}"
                    else:
                        position_range = f"#{min(new_positions)}-#{max(new_positions)}"

                    f.write(f"│  🏷️  原始条目 #{item['original_index']} → 新位置 {position_range:<20}                      │\n")
                    f.write(f"│  ├─ ⏰ 原始时间: {item['original_duration']:<30} │\n")

                    # 处理长文本，确保不超过边界
                    original_text = item['original_text'][:50] + "..." if len(item['original_text']) > 50 else item['original_text']
                    f.write(f"│  ├─ 📝 原始内容: \"{original_text}\"{'  ' * (50 - len(original_text))}│\n")
                    f.write(f"│  └─ ✨ 修复为 {item['new_count']} 个新条目:{'  ' * (50 - len(str(item['new_count'])))}│\n")

                    for j, new_entry in enumerate(item['new_entries']):
                        prefix = "├─" if j < len(item['new_entries']) - 1 else "└─"
                        new_text = new_entry['text'][:35] + "..." if len(new_entry['text']) > 35 else new_entry['text']
                        f.write(f"│      {prefix} 📍 #{new_entry['new_position']:<4} {new_entry['duration']:<20} \"{new_text}\"{'  ' * (35 - len(new_text))}│\n")

                    f.write("│                                                                              │\n")

                f.write("╰──────────────────────────────────────────────────────────────────────────────╯\n\n")

            # 删除详情
            if comparison_report['deleted']:
                f.write("╭─ 🗑️  删除详情 ────────────────────────────────────────────────────────────────╮\n")
                f.write("│                                                                              │\n")

                for item in comparison_report['deleted']:
                    f.write(f"│  ❌ 原始条目 #{item['original_index']} [已删除]{'  ' * (50 - len(str(item['original_index'])))}│\n")
                    f.write(f"│  ├─ ⏰ 原始时间: {item['original_duration']:<30} │\n")
                    f.write(f"│  ├─ 🔍 删除原因: {item['reason']:<30} │\n")

                    # 处理长文本
                    original_text = item['original_text'][:50] + "..." if len(item['original_text']) > 50 else item['original_text']
                    f.write(f"│  └─ 📝 原始内容: \"{original_text}\"{'  ' * (50 - len(original_text))}│\n")
                    f.write("│                                                                              │\n")

                f.write("╰──────────────────────────────────────────────────────────────────────────────╯\n\n")

            # 处理总结
            f.write("╭─ 🎯 处理总结 ────────────────────────────────────────────────────────────────╮\n")
            f.write("│                                                                              │\n")
            f.write(f"│  ✅ 成功处理: {len(comparison_report['replaced'])}个长条目 → {total_new_entries}个修复条目{'  ' * (30 - len(str(len(comparison_report['replaced']))) - len(str(total_new_entries)))}│\n")

            # 统计删除原因
            silent_count = sum(1 for item in comparison_report['deleted'] if '静音' in item['reason'])
            failed_count = len(comparison_report['deleted']) - silent_count
            f.write(f"│  ❌ 删除处理: {len(comparison_report['deleted'])}个长条目 ({silent_count}个静音 + {failed_count}个转录失败){'  ' * (25 - len(str(len(comparison_report['deleted']))) - len(str(silent_count)) - len(str(failed_count)))}│\n")

            effect_desc = "增加" if net_change > 0 else "减少" if net_change < 0 else "保持"
            effect_num = abs(net_change) if net_change != 0 else ""
            f.write(f"│  📈 净效果: {effect_desc}{effect_num}{'个' if effect_num else ''}条目，提升字幕质量{'  ' * (35 - len(effect_desc) - len(str(effect_num)))}│\n")
            f.write(f"│  🎬 输出文件: {base_name}_fixed.srt{'  ' * (45 - len(base_name))}│\n")
            f.write("│                                                                              │\n")
            f.write("╰──────────────────────────────────────────────────────────────────────────────╯\n")

        self.logger.info(f"对比报告已生成: {report_file}")
        return report_file

    def _save_srt_file(self, entries: List[SubtitleEntry], file_path: str):
        """保存SRT文件"""
        with open(file_path, 'w', encoding='utf-8') as f:
            for entry in entries:
                f.write(f"{entry.index}\n")
                f.write(f"{entry.start_time} --> {entry.end_time}\n")
                f.write(f"{entry.text}\n\n")

    def _extract_audio_segment_with_ffmpeg(self, audio_path: str, segment_path: str,
                                          start_seconds: float, end_seconds: float) -> bool:
        """使用FFmpeg切割音频片段（使用atrim滤镜）"""
        try:
            import subprocess

            # 构建FFmpeg命令 - 使用atrim滤镜进行精确切割
            cmd = [
                'ffmpeg', '-y',  # 覆盖输出文件
                '-i', audio_path,  # 输入文件
                '-af', f'atrim=start={start_seconds}:end={end_seconds}',  # 使用atrim滤镜
                segment_path  # 输出文件（WAV格式）
            ]

            self.logger.debug(f"执行FFmpeg命令: {' '.join(cmd)}")

            # 执行命令 - 抑制输出，使用check=True
            try:
                subprocess.run(
                    cmd,
                    check=True,  # 如果返回码非0则抛出异常
                    stdout=subprocess.DEVNULL,  # 抑制stdout
                    stderr=subprocess.DEVNULL,  # 抑制stderr
                    text=False,  # 不使用文本模式
                    timeout=300  # 5分钟超时
                )

                # 验证输出文件
                if os.path.exists(segment_path) and os.path.getsize(segment_path) > 0:
                    self.logger.debug(f"音频片段切割成功: {segment_path}")
                    return True
                else:
                    self.logger.error(f"FFmpeg执行成功但输出文件无效: {segment_path}")
                    return False

            except subprocess.CalledProcessError as e:
                # FFmpeg执行失败
                self.logger.error(f"FFmpeg切割失败 (返回码: {e.returncode})")
                return False

        except subprocess.TimeoutExpired:
            self.logger.error(f"FFmpeg切割超时: {segment_path}")
            return False
        except FileNotFoundError:
            self.logger.error("FFmpeg未找到，请确保已安装FFmpeg")
            return False
        except Exception as e:
            self.logger.error(f"音频片段切割异常: {e}")
            return False
